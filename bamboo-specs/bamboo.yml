---
version: 2
plan:
  project-key: QM
  key: QUEUE
  name: Queue Module
  description: Main branch of the Queue Module
stages:
- Build and Test:
    manual: false
    final: false
    jobs:
    - Build and Test JDK 8
- Deploy:
    manual: false
    final: false
    jobs:
    - Deploy to Maven
- Release:
    manual: true
    final: false
    jobs:
    - Release to Maven
Build and Test JDK 8:
  key: BTJ8
  tasks:
  - checkout:
      force-clean-build: false
      description: Checkout default repository
  - script:
      interpreter: SHELL
      scripts:
      - |-
        #!/bin/bash -eu

        set -x

        docker run -v m2-repo:/root/.m2/repository -v ${PWD}:/module --rm -w="/module" maven:3.9.9-amazoncorretto-8 bash -c 'mvn clean package -B && chmod -R 777 .'
      description: Build and test
  artifact-subscriptions: []
Deploy to Maven:
  key: DTM
  tasks:
  - checkout:
      force-clean-build: false
      description: Checkout default repository
  - script:
      interpreter: SHELL
      scripts:
      - |-
        #!/bin/bash -eu

        set -x

        docker pull ${bamboo.build.docker.image.id}

        docker run -v m2-repo:/root/.m2/repository -v ~/.m2/settings.xml:/.m2/settings.xml:ro -v ${PWD}:/module --rm -w="/module" ${bamboo.build.docker.image.id} bash -c 'mvn deploy -B -DskipTests --settings /.m2/settings.xml'
      description: Deploy
  artifact-subscriptions: []
Release to Maven:
  key: RTM
  tasks:
  - checkout:
      force-clean-build: 'false'
      description: Checkout Default Repository
  - checkout:
      repository: Release scripts
      path: release-scripts
      force-clean-build: 'false'
      description: Checkout Release Scripts Repository
  - script:
      interpreter: SHELL
      scripts:
      - |-
        #!/bin/bash -eu

        set -x

        docker pull ${bamboo.build.docker.image.id}

        docker run \
          -v m2-repo:/root/.m2/repository \
          -v ~/.m2/settings.xml:/root/.m2/settings.xml:ro \
          -v ${PWD}:/module \
          -v ~/.ssh/github:/root/.ssh:ro \
          -e GIT_USER_NAME=$GIT_USER_NAME \
          -e GIT_USER_EMAIL=$GIT_USER_EMAIL \
          -e GIT_SSH_COMMAND='ssh -i /root/.ssh/id_rsa -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no' \
          --rm \
          -w='/module' \
          ${bamboo.build.docker.image.id} \
          bash -c '
            yum -y install git &&
            yum clean all &&
            git config --global user.email "$GIT_USER_EMAIL" &&
            git config --global user.name "$GIT_USER_NAME" &&
            git config --global --add safe.directory /module &&
            ./release-scripts/release-prepare-perform.sh \
              -r ${bamboo.maven.release.version} \
              -d ${bamboo.maven.development.version} \
              -e ${bamboo.planRepository.repositoryUrl}
          '
      environment: GIT_USER_NAME=${bamboo.git.user.name} GIT_USER_EMAIL=${bamboo.git.user.email}
      description: Release prepare perform
  - any-task:
      plugin-key: com.atlassian.bamboo.plugins.variable.updater.variable-updater-generic:variable-extractor
      configuration:
        variable: maven.release.version
        removeSnapshot: 'true'
        variableScope: PLAN
      description: Save next release version
  - any-task:
      plugin-key: com.atlassian.bamboo.plugins.variable.updater.variable-updater-generic:variable-extractor
      configuration:
        variable: maven.development.version
        variableScope: JOB
      description: Retrieve next snaphot version
  artifact-subscriptions: []
variables:
  build.docker.image.id: maven:3.9.9-amazoncorretto-8
  maven.development.version: 2.8.0-SNAPSHOT
  maven.release.version: 2.7.0
repositories:
- Git:
    type: git
    url: https://github.com/openmrs/openmrs-module-queue
    branch: main
    command-timeout-minutes: '180'
    lfs: false
    verbose-logs: false
    use-shallow-clones: true
    cache-on-agents: false
    submodules: false
    submodules-use-shallow-clones: false
    ssh-key-applies-to-submodules: false
    fetch-all: false
- Release scripts:
    scope: global
triggers:
- polling:
    period: '180'
    repositories:
    - Git
branches:
  create: manually
  delete: never
  link-to-jira: true
notifications:
- events:
  - plan-completed
  recipients:
  - emails:
    - <EMAIL>
- events:
  - plan-failed
  recipients:
  - committers
labels: []
dependencies:
  require-all-stages-passing: false
  enabled-for-branches: true
  block-strategy: none
  plans: []
other:
  concurrent-build-plugin:
    number-of-concurrent-builds: system-default
    execution-strategy: block-triggering
---
version: 2
plan:
  key: QM-QUEUE
plan-permissions:
- roles:
  - anonymous
  permissions:
  - view
...

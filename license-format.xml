<?xml version="1.0" encoding="UTF-8" ?>
<!--
    This Source Code Form is subject to the terms of the Mozilla Public License,
    v. 2.0. If a copy of the MPL was not distributed with this file, You can
    obtain one at http://mozilla.org/MPL/2.0/. OpenMRS is also distributed under
    the terms of the Healthcare Disclaimer located at http://openmrs.org/license.
    Copyright (C) OpenMRS Inc. OpenMRS is a registered trademark and the OpenMRS
    graphic logo is a trademark of OpenMRS Inc.
-->
<additionalHeaders>
    <java_style>
        <firstLine>/*</firstLine>
        <beforeEachLine> * </beforeEachLine>
        <endLine> */</endLine>
        <firstLineDetectionPattern>(\s|\t)*/\*.*$</firstLineDetectionPattern>
        <lastLineDetectionPattern>.*\*/(\s|\t)*$</lastLineDetectionPattern>
        <allowBlankLines>false</allowBlankLines>
        <isMultiline>true</isMultiline>
        <padLines>false</padLines>
    </java_style>
    <myxml_style>
        <firstLine><![CDATA[<!--]]></firstLine>
        <beforeEachLine>    </beforeEachLine>
        <endLine><![CDATA[-->]]></endLine>
        <skipLine><![CDATA[^<\?xml.*>$]]></skipLine>
        <firstLineDetectionPattern><![CDATA[(\s|\t)*<!--.*$]]></firstLineDetectionPattern>
        <lastLineDetectionPattern><![CDATA[.*-->(\s|\t)*$]]></lastLineDetectionPattern>
        <allowBlankLines>false</allowBlankLines>
        <isMultiline>true</isMultiline>
        <padLines>false</padLines>
    </myxml_style>
</additionalHeaders>

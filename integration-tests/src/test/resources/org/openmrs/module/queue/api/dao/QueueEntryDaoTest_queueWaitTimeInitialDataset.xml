<?xml version="1.0" encoding="UTF-8" ?>
<!--
    This Source Code Form is subject to the terms of the Mozilla Public License,
    v. 2.0. If a copy of the MPL was not distributed with this file, You can
    obtain one at http://mozilla.org/MPL/2.0/. OpenMRS is also distributed under
    the terms of the Healthcare Disclaimer located at http://openmrs.org/license.

    Copyright (C) OpenMRS Inc. OpenMRS is a registered trademark and the OpenMRS
    graphic logo is a trademark of OpenMRS Inc.
-->
<dataset>
    <queue_entry queue_entry_id="1"  queue_id="1" patient_id="100" creator="1" priority="1001" status="3001" sort_weight="0"
                 date_created="2022-02-02 16:38:56.0" started_at="2022-02-02 16:40:56.0" voided="false" uuid="4eb8fe43-2813-4kbc-80dc-2e5d30252cc6"/>
    <queue_entry queue_entry_id="3"  queue_id="3" patient_id="100" creator="1" priority="1001" status="3001" sort_weight="0"
                 date_created="2022-02-02 16:38:56.0" started_at="2022-02-02 16:40:56.0" ended_at="2022-02-02 18:40:56.0" voided="false" uuid="7ub8fe43-2813-4kbc-80dc-2e5d30252cc5"/>
    <queue_entry queue_entry_id="4"  queue_id="3" patient_id="100" creator="1" priority="1001" status="3002" sort_weight="0"
                 date_created="2022-02-02 16:38:56.0" started_at="2022-02-02 16:40:56.0" ended_at="2022-02-02 19:40:56.0" voided="false" uuid="7ub9b56e-2813-4kbc-80dc-2e5d30252cc5"/>
</dataset>

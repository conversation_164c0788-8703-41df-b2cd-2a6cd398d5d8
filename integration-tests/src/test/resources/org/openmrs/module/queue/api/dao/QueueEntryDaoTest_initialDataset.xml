<?xml version="1.0" encoding="UTF-8" ?>
<!--
    This Source Code Form is subject to the terms of the Mozilla Public License,
    v. 2.0. If a copy of the MPL was not distributed with this file, You can
    obtain one at http://mozilla.org/MPL/2.0/. OpenMRS is also distributed under
    the terms of the Healthcare Disclaimer located at http://openmrs.org/license.

    Copyright (C) OpenMRS Inc. OpenMRS is a registered trademark and the OpenMRS
    graphic logo is a trademark of OpenMRS Inc.
-->
<dataset>
    <queue_entry queue_entry_id="1"
                 uuid="4eb8fe43-2813-4kbc-80dc-2e5d30252cc6"
                 queue_id="1"
                 patient_id="100"
                 visit_id="101"
                 started_at="2022-02-02 16:40:56.0"
                 ended_at="2022-02-02 18:40:56.0"
                 priority="1001"
                 priority_comment="[NULL]"
                 status="3001"
                 sort_weight="0"
                 location_waiting_for="3"
                 provider_waiting_for="1"
                 queue_coming_from="[NULL]"
                 voided="false"
                 voided_by="[NULL]"
                 date_voided="[NULL]"
                 creator="1"
                 date_created="2022-02-02 16:38:56.0"
    />
    <queue_entry queue_entry_id="2"
                 uuid="0ab72588-741b-11ee-9149-0242ac120002"
                 queue_id="2"
                 patient_id="100"
                 visit_id="101"
                 started_at="2022-02-02 18:40:56.0"
                 ended_at="[NULL]"
                 priority="1002"
                 priority_comment="[NULL]"
                 status="3002"
                 sort_weight="10"
                 location_waiting_for="3"
                 provider_waiting_for="[NULL]"
                 queue_coming_from="1"
                 voided="false"
                 voided_by="[NULL]"
                 date_voided="[NULL]"
                 creator="1"
                 date_created="2022-02-02 16:38:56.0"
    />

    <queue_entry queue_entry_id="3"
                 uuid="7ub8fe43-2813-4kbc-80dc-2e5d30252cc5"
                 queue_id="3"
                 patient_id="100"
                 visit_id="102"
                 started_at="2022-02-03 16:40:56.0"
                 ended_at="[NULL]"
                 priority="1001"
                 priority_comment="Comment example"
                 status="3002"
                 sort_weight="20"
                 location_waiting_for="[NULL]"
                 provider_waiting_for="1"
                 queue_coming_from="[NULL]"
                 voided="false"
                 voided_by="[NULL]"
                 date_voided="[NULL]"
                 creator="1"
                 date_created="2022-02-02 16:38:56.0"
    />

    <queue_entry queue_entry_id="4"
                 uuid="05ca9137-741b-11ee-9149-0242ac120002"
                 queue_id="3"
                 patient_id="2"
                 visit_id="[NULL]"
                 started_at="2022-03-02 16:40:56.0"
                 ended_at="2022-03-02 18:41:56.0"
                 priority="1001"
                 priority_comment="[NULL]"
                 status="3002"
                 sort_weight="0"
                 location_waiting_for="1"
                 provider_waiting_for="[NULL]"
                 queue_coming_from="[NULL]"
                 voided="false"
                 voided_by="[NULL]"
                 date_voided="[NULL]"
                 creator="1"
                 date_created="2022-02-02 16:38:56.0"
    />

    <queue_entry queue_entry_id="10"
                 uuid="4gb8fe43-2813-4kbc-80dc-2e5d30252ff0"
                 queue_id="2"
                 patient_id="100"
                 visit_id="101"
                 started_at="2022-02-02 18:40:56.0"
                 ended_at="[NULL]"
                 priority="1001"
                 priority_comment="[NULL]"
                 status="3001"
                 sort_weight="0"
                 location_waiting_for="[NULL]"
                 provider_waiting_for="[NULL]"
                 queue_coming_from="[NULL]"
                 voided="true"
                 voided_by="1"
                 date_voided="2022-02-02 17:40:56.0"
                 creator="1"
                 date_created="2022-02-02 16:38:56.0"
    />

</dataset>

# This workflow will build a Java project with <PERSON><PERSON>, and cache/restore any dependencies to improve the workflow execution time
# For more information see: https://help.github.com/actions/language-and-framework-guides/building-and-testing-java-with-maven

name: Build with <PERSON><PERSON>

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

jobs:
  build:
    strategy:
      matrix:
        platform: [ ubuntu-latest ]
        java-version: [ 8 ]

    runs-on: ${{ matrix.platform }}
    env:
      PLATFORM: ${{ matrix.platform }}
      JAVA_VERSION: ${{ matrix.java-version }}

    steps:
      - uses: actions/checkout@v4
      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          java-version: ${{ matrix.java-version }}
          distribution: 'temurin'
          cache: 'maven'
      - name: Install dependencies
        run: mvn clean install -DskipTests=true -Dmaven.javadoc.skip=true --batch-mode --show-version --file pom.xml
      - name: Build with <PERSON><PERSON>
        run: mvn test --batch-mode --file pom.xml

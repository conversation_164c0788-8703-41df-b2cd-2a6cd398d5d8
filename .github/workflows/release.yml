on:
  push:
    tags:
      - '*' # Create release for every new tag

name: Create Release

jobs:
  build:
    name: Create Release
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2
      - name: Create Release Notes
        uses: openmrs/openmrs-contrib-create-release-notes@v1.0.3
        id: create-release-notes
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with: 
          head-ref: ${{ github.ref }}
      - name: Create Release
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref }}
          release_name: ${{ github.ref }}
          body: |
            ## What's Changed
            ${{ steps.create-release-notes.outputs.release-notes }}
          draft: false
          prerelease: false

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.openmrs.module</groupId>
        <artifactId>queue</artifactId>
        <version>2.7.0-SNAPSHOT</version>
    </parent>

    <artifactId>queue-omod</artifactId>
    <packaging>jar</packaging>
    <name>Queue OMOD</name>
    <description>Omod submodule for Queue</description>

    <dependencies>
        <dependency>
            <groupId>org.openmrs.module</groupId>
            <artifactId>queue-api</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>org.openmrs.web</groupId>
            <artifactId>openmrs-web</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.openmrs.module</groupId>
            <artifactId>webservices.rest-omod</artifactId>
        </dependency>
        <dependency>
            <groupId>org.openmrs.module</groupId>
            <artifactId>webservices.rest-omod-common</artifactId>
        </dependency>

        <dependency>
            <groupId>org.openmrs.web</groupId>
            <artifactId>openmrs-web</artifactId>
            <scope>provided</scope>
            <classifier>tests</classifier>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.parent.artifactId}-${project.parent.version}</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/webapp</directory>
                <filtering>false</filtering>
                <excludes>
                    <exclude>resources</exclude>
                </excludes>
                <targetPath>web/module</targetPath>
            </resource>
            <resource>
                <directory>src/main/webapp</directory>
                <filtering>false</filtering>
                <includes>
                    <include>resources</include>
                </includes>
                <targetPath>web/module</targetPath>
            </resource>
        </resources>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
                <filtering>true</filtering>
            </testResource>
        </testResources>
        <plugins>
            <plugin>
                <groupId>org.commonjava.maven.plugins</groupId>
                <artifactId>directory-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.mycila</groupId>
                <artifactId>license-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>net.revelc.code.formatter</groupId>
                <artifactId>formatter-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>net.revelc.code</groupId>
                <artifactId>impsort-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.openmrs.maven.plugins</groupId>
                <artifactId>maven-openmrs-plugin</artifactId>
                <version>1.0.1</version>
                <extensions>true</extensions>
                <executions>
                    <execution>
                        <id>init</id>
                        <phase>initialize</phase>
                        <goals>
                            <goal>initialize-module</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>pack</id>
                        <phase>package</phase>
                        <goals>
                            <goal>package-module</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>3.2.0</version>
                <executions>
                    <execution>
                        <id>Expand moduleApplicationContext and messages</id>
                        <goals>
                            <goal>unpack-dependencies</goal>
                        </goals>
                        <phase>generate-resources</phase>
                        <configuration>
                            <includeGroupIds>${project.parent.groupId}</includeGroupIds>
                            <includeArtifactIds>${project.parent.artifactId}-api</includeArtifactIds>
                            <excludeTransitive>true</excludeTransitive>
                            <includes>**/*</includes>
                            <outputDirectory>${project.build.directory}/classes</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <properties>
        <main.basedir>${project.parent.basedir}</main.basedir>
    </properties>

</project>

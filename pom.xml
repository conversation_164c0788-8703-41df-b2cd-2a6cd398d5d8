<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>org.openmrs.module</groupId>
    <artifactId>queue</artifactId>
    <version>2.7.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>Queue</name>
    <description>Patient Queues</description>

    <url>https://wiki.openmrs.org/x/FQ4z</url>

    <scm>
        <connection>scm:git:**************:openmrs/openmrs-module-queue.git</connection>
        <developerConnection>scm:git:**************:openmrs/openmrs-module-queue.git</developerConnection>
        <url>https://github.com/openmrs/openmrs-module-queue/</url>
      <tag>HEAD</tag>
  </scm>

    <modules>
        <module>api</module>
        <module>omod</module>
        <module>integration-tests</module>
    </modules>

    <repositories>
        <repository>
            <id>openmrs-repo</id>
            <name>OpenMRS Nexus Repository</name>
            <url>https://mavenrepo.openmrs.org/public</url>
        </repository>
        <repository>
            <id>central</id>
            <name>Maven Repository Switchboard</name>
            <layout>default</layout>
            <url>https://repo1.maven.org/maven2</url>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>openmrs-repo</id>
            <name>OpenMRS Nexus Repository</name>
            <url>https://mavenrepo.openmrs.org/public</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

    <distributionManagement>
        <repository>
            <id>openmrs-repo-modules</id>
            <name>OpenMRS Nexus Modules</name>
            <url>https://mavenrepo.openmrs.org/modules</url>
        </repository>
        <snapshotRepository>
            <id>openmrs-repo-snapshots</id>
            <name>OpenMRS Nexus Snapshots</name>
            <url>https://mavenrepo.openmrs.org/snapshots</url>
        </snapshotRepository>
    </distributionManagement>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.openmrs.api</groupId>
                <artifactId>openmrs-api</artifactId>
                <version>${openmrsPlatformVersion}</version>
                <type>jar</type>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.openmrs.web</groupId>
                <artifactId>openmrs-web</artifactId>
                <version>${openmrsPlatformVersion}</version>
                <type>jar</type>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombokVersion}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.openmrs.module</groupId>
                <artifactId>webservices.rest-omod</artifactId>
                <version>${webservicesRestVersion}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>org.openmrs.module</groupId>
                <artifactId>webservices.rest-omod-common</artifactId>
                <version>${webservicesRestVersion}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>org.openmrs.api</groupId>
                <artifactId>openmrs-api</artifactId>
                <version>${openmrsPlatformVersion}</version>
                <type>test-jar</type>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.openmrs.web</groupId>
                <artifactId>openmrs-web</artifactId>
                <version>${openmrsPlatformVersion}</version>
                <classifier>tests</classifier>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.openmrs.test</groupId>
                <artifactId>openmrs-test</artifactId>
                <version>${openmrsPlatformVersion}</version>
                <type>pom</type>
                <scope>test</scope>
                <exclusions>
                    <exclusion>
                        <groupId>org.mockito</groupId>
                        <artifactId>mockito-all</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.hamcrest</groupId>
                        <artifactId>hamcrest-all</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.powermock</groupId>
                        <artifactId>powermock-module-junit4</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.powermock</groupId>
                        <artifactId>powermock-api-mockito</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- End OpenMRS core -->

            <!-- Test dependencies -->
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>3.10.0</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.hamcrest</groupId>
                <artifactId>hamcrest-core</artifactId>
                <version>2.2</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4</artifactId>
                <version>${powerMockitoVersion}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-api-mockito2</artifactId>
                <version>${powerMockitoVersion}</version>
                <scope>test</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.openmrs.web</groupId>
            <artifactId>openmrs-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.openmrs.test</groupId>
            <artifactId>openmrs-test</artifactId>
            <type>pom</type>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.hamcrest</groupId>
            <artifactId>hamcrest-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
        </dependency>
    </dependencies>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-dependency-plugin</artifactId>
                    <version>3.1.2</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>3.1.2</version>
                    <configuration>
                        <runOrder>alphabetical</runOrder>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.openmrs.maven.plugins</groupId>
                    <artifactId>maven-openmrs-plugin</artifactId>
                    <version>1.0.1</version>
                </plugin>
                <plugin>
                    <groupId>org.commonjava.maven.plugins</groupId>
                    <artifactId>directory-maven-plugin</artifactId>
                    <version>0.1</version>
                    <executions>
                        <execution>
                            <id>directories</id>
                            <goals>
                                <goal>highest-basedir</goal>
                            </goals>
                            <phase>validate</phase>
                            <configuration>
                                <property>main.basedir</property>
                            </configuration>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>com.mycila</groupId>
                    <artifactId>license-maven-plugin</artifactId>
                    <version>3.0</version>
                    <configuration>
                        <header>${main.basedir}${file.separator}license-header.txt</header>
                        <headerDefinitions>
                            <headerDefinition>${main.basedir}${file.separator}license-format.xml</headerDefinition>
                        </headerDefinitions>
                        <mapping>
                            <java>JAVA_STYLE</java>
                            <xml>MYXML_STYLE</xml>
                        </mapping>
                        <includes>
                            <include>**/*.java</include>
                            <include>**/*.xml</include>
                            <include>**/*.properties</include>
                        </includes>
                        <excludes>
                            <exclude>license-format.xml</exclude>
                            <exclude>**/pom.xml</exclude>
                            <exclude>**/target/**</exclude>
                            <exclude>.git/**</exclude>
                            <exclude>.idea/**</exclude>
                            <exclude>.settings/**</exclude>
                            <exclude>.externalToolBuilders/</exclude>
                            <exclude>nbproject/private/</exclude>
                            <exclude>.vscode/**</exclude>
                        </excludes>
                    </configuration>
                    <executions>
                        <execution>
                            <id>add-license</id>
                            <goals>
                                <goal>format</goal>
                            </goals>
                            <phase>validate</phase>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>net.revelc.code.formatter</groupId>
                    <artifactId>formatter-maven-plugin</artifactId>
                    <version>2.13.0</version>
                    <configuration>
                        <lineEnding>LF</lineEnding>
                        <configFile>eclipse/OpenMRSFormatter.xml</configFile>
                    </configuration>
                    <dependencies>
                        <dependency>
                            <groupId>org.openmrs.tools</groupId>
                            <artifactId>openmrs-tools</artifactId>
                            <version>${openmrsPlatformToolsVersion}</version>
                        </dependency>
                    </dependencies>
                    <executions>
                        <execution>
                            <goals>
                                <goal>format</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>net.revelc.code</groupId>
                    <artifactId>impsort-maven-plugin</artifactId>
                    <version>1.5.0</version>
                    <dependencies>
                        <!-- Needed because of maven 3.9.0 backward compatibility issue,
                             See : https://github.com/revelc/impsort-maven-plugin/issues/64 -->
                        <dependency>
                            <groupId>org.codehaus.plexus</groupId>
                            <artifactId>plexus-utils</artifactId>
                            <version>3.5.1</version>
                        </dependency>
                    </dependencies>
                    <configuration>
                        <groups>javax, java, *</groups>
                        <removeUnused>true</removeUnused>
                    </configuration>
                    <executions>
                        <execution>
                            <goals>
                                <goal>sort</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <!-- Fix for "Failed to execute goal org.apache.maven.plugins:maven-resources-plugin:3.2.0:resources (default-resources) on project application-etude: Input length = 1" -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.1.0</version>
                </plugin>

            </plugins>
        </pluginManagement>
    </build>

    <properties>
        <main.basedir>${project.basedir}</main.basedir>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.build.outputEncoding>UTF-8</project.build.outputEncoding>
        <lombokVersion>1.18.20</lombokVersion>
        <powerMockitoVersion>2.0.9</powerMockitoVersion>
        <webservicesRestVersion>2.32.0</webservicesRestVersion>
        <openmrsPlatformVersion>2.3.4</openmrsPlatformVersion>
        <openmrsPlatformToolsVersion>2.4.0</openmrsPlatformToolsVersion>
    </properties>
</project>

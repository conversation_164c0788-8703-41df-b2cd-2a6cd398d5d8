<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.openmrs.module</groupId>
        <artifactId>queue</artifactId>
        <version>2.7.0-SNAPSHOT</version>
    </parent>

    <artifactId>queue-api</artifactId>
    <packaging>jar</packaging>
    <name>Queue API</name>
    <description>API project for Queue</description>

    <dependencies>
        <dependency>
            <groupId>org.openmrs.api</groupId>
            <artifactId>openmrs-api</artifactId>
        </dependency>

        <!--Test dependencies-->
        <dependency>
            <groupId>org.openmrs.api</groupId>
            <artifactId>openmrs-api</artifactId>
            <type>test-jar</type>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
                <filtering>true</filtering>
            </testResource>
        </testResources>
        <plugins>
            <plugin>
                <groupId>org.commonjava.maven.plugins</groupId>
                <artifactId>directory-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.mycila</groupId>
                <artifactId>license-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>net.revelc.code.formatter</groupId>
                <artifactId>formatter-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>net.revelc.code</groupId>
                <artifactId>impsort-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <properties>
        <main.basedir>${project.parent.basedir}</main.basedir>
    </properties>
</project>

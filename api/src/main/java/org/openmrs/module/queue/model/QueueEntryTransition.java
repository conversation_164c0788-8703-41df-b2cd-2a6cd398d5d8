/*
 * This Source Code Form is subject to the terms of the Mozilla Public License,
 * v. 2.0. If a copy of the MPL was not distributed with this file, You can
 * obtain one at http://mozilla.org/MPL/2.0/. OpenMRS is also distributed under
 * the terms of the Healthcare Disclaimer located at http://openmrs.org/license.
 *
 * Copyright (C) OpenMRS Inc. OpenMRS is a registered trademark and the OpenMRS
 * graphic logo is a trademark of OpenMRS Inc.
 */
package org.openmrs.module.queue.model;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import org.openmrs.Concept;

/**
 * Bean definition that encapsulates the supported criteria for saving a direct transition from one
 * QueueEntry to another QueueEntry.
 */
@Data
public class QueueEntryTransition implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
	private QueueEntry queueEntryToTransition;
	
	private Date transitionDate;
	
	private Queue newQueue;
	
	private Concept newStatus;
	
	private Concept newPriority;
	
	private String newPriorityComment;
	
	/**
	 * @return a new queue entry representing what one intends to transition into
	 */
	public QueueEntry constructNewQueueEntry() {
		QueueEntry queueEntry = new QueueEntry();
		queueEntry.setQueue(newQueue == null ? queueEntryToTransition.getQueue() : newQueue);
		queueEntry.setPatient(queueEntryToTransition.getPatient());
		queueEntry.setVisit(queueEntryToTransition.getVisit());
		queueEntry.setPriority(newPriority == null ? queueEntryToTransition.getPriority() : newPriority);
		queueEntry.setPriorityComment(
		    newPriorityComment == null ? queueEntryToTransition.getPriorityComment() : newPriorityComment);
		queueEntry.setStatus(newStatus == null ? queueEntryToTransition.getStatus() : newStatus);
		queueEntry.setSortWeight(queueEntryToTransition.getSortWeight());
		queueEntry.setLocationWaitingFor(queueEntryToTransition.getLocationWaitingFor());
		queueEntry.setProviderWaitingFor(queueEntryToTransition.getProviderWaitingFor());
		queueEntry.setQueueComingFrom(queueEntryToTransition.getQueue());
		queueEntry.setStartedAt(transitionDate);
		return queueEntry;
	}
}
